import mongoose, { Schema, model, models, Types } from "mongoose";
import { IUser } from "./User";

export interface IBlogPost {
  _id: Types.ObjectId;
  title: string;
  content: string;
  slug: string;
  description?: string;
  tags: string[];
  categoryId?: Types.ObjectId;
  categoryName?: string;
  metaTitle?: string;
  metaDescription?: string;
  status: "draft" | "scheduled" | "published" | "archived";
  visibility: "public" | "private" | "draft";
  coverImage?: string;
  featuredImage?: string;
  imageAlt?: string;
  credit?: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  scheduledAt?: Date;
  authorId: Types.ObjectId | IUser;
}

const blogPostSchema = new Schema<IBlogPost>(
  {
    title: {
      type: String,
      required: [true, "Please provide a title"],
      trim: true,
    },
    content: {
      type: String,
      required: [true, "Please provide content"],
    },
    slug: {
      type: String,
      required: [true, "Please provide a slug"],
      unique: true,
      trim: true,
      lowercase: true,
    },
    description: {
      type: String,
      trim: true,
    },
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category",
    },
    categoryName: {
      type: String,
      trim: true,
    },
    metaTitle: {
      type: String,
      trim: true,
    },
    metaDescription: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ["draft", "scheduled", "published", "archived"],
      default: "draft",
    },
    visibility: {
      type: String,
      enum: ["public", "private", "draft"],
      default: "public",
    },
    coverImage: {
      type: String,
      trim: true,
    },
    featuredImage: {
      type: String,
      trim: true,
    },
    imageAlt: {
      type: String,
      trim: true,
    },
    credit: {
      type: String,
      trim: true,
    },
    publishedAt: {
      type: Date,
    },
    scheduledAt: {
      type: Date,
      default: Date.now,
    },
   authorId: {
  type: mongoose.Schema.Types.ObjectId,
  ref: "User", // <- Must exactly match model name "User"
  required: true,
},
  },
  { timestamps: true }
);

// Create a text index for search functionality
blogPostSchema.index(
  { title: "text", content: "text", tags: "text" },
  { weights: { title: 10, tags: 5, content: 1 } }
);

// Note: We don't need to create an index on slug here
// because it's already defined as unique: true in the schema

// Generate slug from title if not provided
blogPostSchema.pre("save", function (next) {
  const doc = this as any;
  if (doc.isModified("title") && !doc.slug) {
    doc.slug = doc.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, "")
      .replace(/\s+/g, "-");
  }
  next();
});

const BlogPost =
  models.BlogPost || model<IBlogPost>("BlogPost", blogPostSchema);

export default BlogPost;
