
01:fix this error 
[{
	"resource": "/C:/Users/<USER>/Desktop/ToolBox/version/src/components/layout/Header.tsx",
	"owner": "typescript",
	"message": "Property 'catch' does not exist on type 'void'.",
}]

02:header signup button redirect signup route not exist don't change button name and anything just replace signup route to register
03:signup and login form input active color not visible i can't see any word 
04:login form i need some better design like signup is very good and add password eye toggle visible or not and implement forget route and functionalities 
05:  fix this error    ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET /_next/static/webpack/78ee464c3f521d46.webpack.hot-update.json 404 in 5383ms
 06: GET /api/analytics?type=summary&range=month 400 in 61ms i need proper fetch data 
 07: remove subcription with email from blog page button component everything remove related subcription 
 08:remove blog stats page from admin panel, and Home in sidebar
 09:remove complete comments from blog page and admin page in sidebar delete comments page from admin panel


  create SEO Settings Admin Panel UI:
Prompt:

"Design a modern, clean SEO Settings admin dashboard page for a website. The layout should include:

🔧 A vertical sidebar on the left with navigation items: 'General', 'SEO Settings', 'Appearance', 'Integrations'.

📄 On the main page area, include these SEO fields:

Site Title

Site Description

Meta Keywords

Default Share Image (upload input with image preview)

Robots.txt settings (toggle switch: Allow / Disallow)

Canonical URL input

Open Graph Title & Description

Twitter Card settings

Favicon uploader

Sitemap toggle (Enable/Disable)

Google Analytics ID field

🔴 The header should say “SEO Settings”, with save and reset buttons at the top-right.

🎨 Style should be professional, flat UI, with cards, soft shadows, rounded corners, and real inputs/text.
Sticky top header bar with solid background (not transparent).
Show tooltips or small help texts beside complex fields.

💻 Web dashboard view, light mode."

🛠️ Optional Enhancements:
Include a live preview snippet for how title/description would look in Google.

Add a tab switcher for Basic vs Advanced SEO.

Mobile-responsive layout with drawer sidebar.




 ○ Compiling /tools ...
 ✓ Compiled /tools in 4.6s (4341 modules)
Using cached database connection
 ○ Compiling /tools/pdf-to-word ...
 ✓ Compiled /tools/pdf-to-word in 7.8s (4375 modules)
Using cached database connection
 ○ Compiling /tools/[tool] ...
 ✓ Compiled /tools/[tool] in 5.1s (4410 modules)
 ○ Compiling /tools/pdf-to-excel ...
 ✓ Compiled /tools/pdf-to-excel in 1217ms (4417 module


  ✓ Compiled /api/analytics in 2.1s (2411 modules)
Using cached database connection
 POST /api/auth/callback/credentials 401 in 3475ms

 Create the following pages for a professional SaaS-style document tool and blog platform:

Privacy Policy Page

Use standard legal language as per GDPR and Google’s compliance.

Explain what user data is collected, how it's stored, used, and shared.

Mention use of cookies, analytics (e.g. Google Analytics), third-party tools, and email communication.

Include sections like:

What We Collect

Why We Collect It

Data Retention

Security Measures

User Rights

Contact for Privacy Questions

Terms and Conditions Page

Clearly define user responsibilities, usage limits, and intellectual property ownership.

Mention rules for using tools, fair usage policies, and content generation boundaries.

Sections:

Introduction

Acceptance of Terms

User Obligations

Tool Usage License

Content Ownership and AI Usage

Limitation of Liability

Termination of Access

Contact Us Page

Professional contact form UI with name, email, message, category (feedback/support/bug).

Form should have validation, submission loading state, success/error messages.

Use Framer Motion for subtle animation (form slide in or fade in).

Add contact email and business location (if applicable).

About Us Page

Add a section for:

Platform goals

Team introduction

Mission statement

Technologies used (Next.js, MongoDB, TailwindCSS, etc.)

Links to GitHub or social media (optional)

Add these links to the website footer (not in header):

Privacy Policy

Terms & Conditions

About Us

Contact Us

Sign-up Page Functional Requirement:

Add a required checkbox: “I agree to the Terms and Privacy Policy”

Link "Terms" and "Privacy Policy" to their respective pages.

Prevent form submission if the box is unchecked and show an error toast or message.

Add light animation (e.g. button shake or warning highlight on error).

🎨 Design Tips (for prompt or manual implementation)
Use soft gradient backgrounds for About & Contact pages.

Add simple motion effects via Framer Motion (fadeIn, slideIn, or staggered list).

Maintain consistency: Use black/white themes with proper spacing and legible typography.

Footer links should be styled cleanly with hover effects (e.g., underline on hover, opacity-75, text-sm).

Use headless UI or shadcn/ui components for accessibility and responsiveness.