"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { Calendar, User, ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: string;
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
  category?: string;
  imageCredit?: string;
}

interface UniformBlogCardProps {
  post: BlogPost;
  index: number;
  showAnimation?: boolean;
  className?: string;
}

export function UniformBlogCard({ 
  post, 
  index, 
  showAnimation = true,
  className = ""
}: UniformBlogCardProps) {
  
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Recent';
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      variants={showAnimation ? cardVariants : undefined}
      transition={showAnimation ? { duration: 0.5, delay: index * 0.1 } : undefined}
      whileHover={{ y: -8 }}
      className={`group ${className}`}
    >
      <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
        <div className="relative h-full flex flex-col">
          {/* Image section with fixed height */}
          <div className="relative h-48 overflow-hidden">
            <Link href={`/blog/${post.slug}`}>
              <img
                src={post.featuredImage || '/images/blog-placeholder.jpg'}
                alt={post.title}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/blog-placeholder.jpg';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>
            
            {/* Category badge */}
            {post.category && (
              <div className="absolute top-4 left-4">
                <Badge 
                  variant="secondary" 
                  className="bg-white/90 text-gray-800 hover:bg-white transition-colors"
                >
                  {post.category}
                </Badge>
              </div>
            )}
          </div>

          {/* Content section with flex-grow to take remaining space */}
          <CardContent className="p-6 flex-grow flex flex-col">
            {/* Date */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>

            {/* Title */}
            <Link href={`/blog/${post.slug}`}>
              <h3 className="font-bold text-lg leading-tight mb-3 text-foreground group-hover:text-primary transition-colors line-clamp-2">
                {truncateText(post.title, 60)}
              </h3>
            </Link>

            {/* Excerpt */}
            <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3 mb-4 flex-grow">
              {truncateText(post.excerpt, 120)}
            </p>

            {/* Author and Read More section - now fixed at bottom */}
            <div className="flex items-center justify-between pt-4 border-t border-border mt-auto">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {post.author.name}
                </span>
              </div>
              
              <Link 
                href={`/blog/${post.slug}`}
                className="inline-flex items-center gap-1 text-sm font-medium text-primary hover:text-primary/80 transition-colors group/link"
              >
                Read More
                <ArrowRight className="h-4 w-4 transition-transform group-hover/link:translate-x-1" />
              </Link>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
}