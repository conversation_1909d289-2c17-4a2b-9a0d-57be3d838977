"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { UnifiedBlogCard } from "./UnifiedBlogCard";

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  content?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  imageCredit?: string;
  categoryId?: string;
  category?: string;
  categories?: string[];
  tags?: string[];
  publishedAt?: string;
  createdAt?: string;
  date?: string;
  author: {
    name: string;
    email?: string;
  } | string;
}

interface UnifiedBlogGridProps {
  posts: BlogPost[];
  loading?: boolean;
  showAnimation?: boolean;
  className?: string;
}

export function UnifiedBlogGrid({ 
  posts, 
  loading = false, 
  showAnimation = true,
  className = ""
}: UnifiedBlogGridProps) {
  const [mounted, setMounted] = useState(false);

  // Handle mounting to prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  // Loading skeleton
  if (loading || !mounted) {
    return (
      <div className={`${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="h-[400px]  bg-muted rounded-2xl animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  // Empty state
  if (!posts || posts.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center py-12"
      >
        <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center mb-6">
          <div className="text-muted-foreground text-sm">No Posts</div>
        </div>
        <h3 className="text-xl font-medium mb-2 text-foreground">
          No articles found
        </h3>
        <p className="text-muted-foreground">
          Check back later for new content or try adjusting your search filters.
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={showAnimation ? containerVariants : undefined}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      className={`${className}`}
    >
      {/* Unified Grid Layout with Equal Height Cards */}
      <div className={`
        grid gap-8
        grid-cols-1 
        md:grid-cols-2 
        lg:grid-cols-3
        ${posts.length === 3 ? 'lg:justify-center lg:max-w-5xl lg:mx-auto' : ''}
      `}>
        {posts.map((post, index) => (
          <motion.div
            key={post.id || post._id || index}
            variants={showAnimation ? itemVariants : undefined}
            className="h-full"
          >
            <UnifiedBlogCard
              post={post}
              index={index}
              showAnimation={false} // We handle animation here
              className="h-full min-h-[400px] max-w-[350px] mx-auto"
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
