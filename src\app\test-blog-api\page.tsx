'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestBlogApiPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testEndpoint = async (endpoint: string, name: string) => {
    try {
      setLoading(true);
      console.log(`Testing ${name}: ${endpoint}`);
      
      const response = await fetch(endpoint);
      const data = await response.json();
      
      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          success: response.ok,
          data: data,
          error: response.ok ? null : `HTTP ${response.status}`
        }
      }));
      
      console.log(`${name} result:`, { status: response.status, data });
    } catch (error) {
      console.error(`${name} error:`, error);
      setResults(prev => ({
        ...prev,
        [name]: {
          status: 'ERROR',
          success: false,
          data: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    setResults({});
    
    // Test all blog-related endpoints
    await testEndpoint('/api/blog?status=published&limit=5', 'Blog API');
    await testEndpoint('/api/blog/recent?limit=3', 'Recent Posts API');
    await testEndpoint('/api/admin/blog/posts?limit=3', 'Admin Blog API');
    await testEndpoint('/api/posts?status=published&limit=3', 'Posts API');
  };

  useEffect(() => {
    runAllTests();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Blog API Test</h1>
          <p className="text-muted-foreground">
            Testing all blog-related API endpoints to diagnose issues
          </p>
        </div>

        <Button onClick={runAllTests} disabled={loading}>
          {loading ? 'Testing...' : 'Run Tests Again'}
        </Button>

        <div className="grid gap-4">
          {Object.entries(results).map(([name, result]: [string, any]) => (
            <Card key={name}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {name}
                  <span className={`px-2 py-1 rounded text-xs ${
                    result.success 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {result.status}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result.error && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded">
                    <p className="text-red-600 dark:text-red-400 font-medium">Error:</p>
                    <p className="text-red-600 dark:text-red-400">{result.error}</p>
                  </div>
                )}
                
                {result.data && (
                  <div>
                    <p className="font-medium mb-2">Response Data:</p>
                    <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-64">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
